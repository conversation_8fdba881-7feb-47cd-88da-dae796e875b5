# Agent Mode Feature Documentation

## Overview

The Agent Mode feature enhances the DeepSite application by allowing users to switch between "Normal Mode" and "Agent Mode" with specialized AI assistants. Each agent has unique expertise and instructions that optimize HTML generation for specific use cases.

## Features Implemented

### 1. Frontend Components (React)

#### AskAI Component Enhancements
- **Agent Mode Toggle**: Blue toggle button to switch between Normal and Agent modes
- **Agent Selection Dropdown**: Dropdown menu to select from available agents
- **Visual Indicators**: 
  - Blue color scheme when Agent Mode is active
  - Agent name displayed in active state
  - Dynamic placeholder text showing selected agent
- **Responsive UI**: Works on both desktop and mobile devices

#### Key UI Elements
- Agent Mode toggle button with robot icon
- Agent selection dropdown with agent names and descriptions
- Visual feedback showing active agent
- Color-coded interface (blue for agent mode, pink for normal mode)

### 2. Backend Implementation (Node.js/Express)

#### Agent Loader System (`utils/agentLoader.js`)
- **Dynamic Agent Loading**: Automatically loads all `.md` files from `agents/` directory
- **Agent Parsing**: Extracts agent names and descriptions from markdown files
- **System Prompt Generation**: Creates specialized system prompts for each agent
- **Caching**: Agents are loaded once at startup for optimal performance

#### API Endpoints
- **`GET /api/agents`**: Returns list of available agents with metadata
- **`POST /api/ask-ai`**: Enhanced to handle agent mode parameters
  - Accepts `agentMode` and `agentName` parameters
  - Uses agent-specific system prompts when agent mode is enabled
  - Falls back to default behavior for normal mode

### 3. Agent System

#### Agent File Structure
All agent files are stored in `sd/agents/` directory as markdown files:
- `cheonwon.md` - Devin (Software Engineer)
- `eren.md` - DataForge (Database Expert)
- `flora.md` - Bolt (Full-stack Developer)
- `isha.md` - Isha (AI Assistant)
- `isya.md` - V0 (Vercel's AI Assistant)
- `levi.md` - Lovable (Web App Creator)
- `mikasa.md` - Mikasa (DevOps Expert)
- `roro.md` - An (Autonomous Programmer)
- `rumi .md` - Cline (Software Engineer)
- `ruru.md` - Roo (Software Engineer)
- `shinmen.md` - Shinmen (AI Assistant)
- `takezo.md` - An (Programming Assistant)

#### Agent Capabilities
Each agent brings specialized knowledge:
- **Database Design** (DataForge)
- **DevOps & Infrastructure** (Mikasa)
- **Full-stack Development** (Bolt)
- **Web Application Creation** (Lovable)
- **System Programming** (Devin)
- And more...

## Technical Implementation

### Frontend Architecture
```typescript
interface Agent {
  name: string;
  filename: string;
  description: string;
}
```

### Backend Architecture
```javascript
class AgentLoader {
  constructor(agentsDir)
  loadAgents()
  parseAgent(filename, content)
  getAgents()
  getAgent(filename)
  createAgentSystemPrompt(filename, basePrompt)
}
```

### API Request Format
```json
{
  "prompt": "User's request",
  "html": "Current HTML content",
  "previousPrompt": "Previous request",
  "agentMode": true,
  "agentName": "eren.md"
}
```

## Usage Instructions

### For Users
1. **Enable Agent Mode**: Click the "Agent Mode" toggle button
2. **Select Agent**: Choose an agent from the dropdown menu
3. **Ask Questions**: Type your request - the selected agent will respond with specialized knowledge
4. **Switch Agents**: Change agents anytime by selecting a different one from the dropdown
5. **Disable Agent Mode**: Click the toggle again to return to normal mode

### For Developers
1. **Add New Agents**: Place new `.md` files in the `agents/` directory
2. **Agent Format**: Start with "You are [Name], [description]..." for proper parsing
3. **Restart Server**: New agents are loaded at startup
4. **Test Agents**: Use the `/api/agents` endpoint to verify agent loading

## Benefits

### Enhanced HTML Generation
- **Specialized Knowledge**: Each agent brings domain-specific expertise
- **Context-Aware Responses**: Agents understand their role and provide relevant solutions
- **Consistent Quality**: Agents maintain their expertise while adapting to web development

### Improved User Experience
- **Visual Feedback**: Clear indication when agent mode is active
- **Easy Switching**: Simple toggle and dropdown interface
- **Specialized Assistance**: Users can choose the right expert for their task

### Scalable Architecture
- **Modular Design**: Easy to add new agents
- **Performance Optimized**: Agents loaded once at startup
- **Backward Compatible**: Normal mode still works as before

## Future Enhancements

- Agent-specific templates and examples
- Agent conversation history
- Custom agent creation interface
- Agent performance analytics
- Multi-agent collaboration features

## Troubleshooting

### Common Issues
1. **Agents not loading**: Check that agent files are in `sd/agents/` directory
2. **Agent mode not working**: Verify server restart after adding new agents
3. **UI not updating**: Clear browser cache and refresh

### Debug Information
- Server logs show agent loading status
- Browser console shows API requests
- `/api/agents` endpoint lists available agents
